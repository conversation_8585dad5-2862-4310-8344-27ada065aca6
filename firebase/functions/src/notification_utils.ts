import { admin } from "./config";
import { logger } from "firebase-functions/v2";
import { isProduction } from "./environment";

/**
 * Allowed user IDs for push notifications in development mode
 * Push notifications will only be sent to these users when running in dev mode
 */
const ALLOWED_DEV_USER_IDS = [
  "c5YHX79mg8OgHoyF12JBEffYwnE3",
  "oEay2mlxWgXAYRyKxvuIjL54pII3", 
  "zH5hYbVBkwPUc99Q2asR5TIXnks1"
];

/**
 * Send push notification with development mode guard
 * In production: sends all notifications
 * In development: only sends notifications to allowed user IDs
 */
export async function sendPushNotification(message: admin.messaging.Message, context: string, userId: string): Promise<void> {
  const isProd = isProduction();
  
  // In production, always send notifications
  // In development, only send if userId is in the allowed list
  const shouldSend = isProd || (userId && ALLOWED_DEV_USER_IDS.includes(userId));

  if (shouldSend) {
    // Send the actual notification
    await admin.messaging().send(message);

    const environmentEmoji = isProd ? "📱" : "🔔";
    const environmentLabel = isProd ? "PRODUCTION" : "DEV MODE";

    logger.info(`${environmentEmoji} [${environmentLabel}] Push notification sent successfully`, {
      context,
      environment: isProd ? "production" : "development",
      to: "token" in message ? "single_device" : "topic",
      userId: userId,
      title: message.notification?.title,
      body: message.notification?.body,
    });
  } else {
    // Log the notification details in dev mode when disabled
    const token = "token" in message ? message.token : null;
    const blockedReason = !isProd && !ALLOWED_DEV_USER_IDS.includes(userId) 
      ? `User ${userId} not in allowed dev list` 
      : "Notifications disabled";
      
    logger.info(`🔔 [DEV MODE - BLOCKED] Push notification not sent:`, {
      context,
      environment: "development",
      reason: blockedReason,
      userId: userId,
      title: message.notification?.title,
      body: message.notification?.body,
      data: message.data,
      token: token ? `${token.substring(0, 20)}...` : "N/A",
      android: message.android ? "configured" : "not_configured",
      apns: message.apns ? "configured" : "not_configured",
      webpush: message.webpush ? "configured" : "not_configured",
    });
  }
}
