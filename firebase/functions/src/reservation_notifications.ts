import * as admin from "firebase-admin";
import { logger } from "firebase-functions/v2";
import { db } from "./config";
import { sendPassengerNotification, sendDriverAssignmentNotification } from "./notifications";
import { sendAdminNotification } from "./chat_notifications";
import { getReservationReminderTimes } from "./notification_engine";
import { Timestamp } from "firebase-admin/firestore";
import { getTenantCollection, getDefaultTenantId, getAllActiveTenants } from "./tenant_utils";

interface Trip {
  uidPassenger: string;
  uidChosenDriver?: string;
  status: string;
  pickupTime?: Timestamp;
  startLocation?: {
    name: string;
    lat: number;
    lng: number;
  };
  arrivalLocation?: {
    name: string;
    lat: number;
    lng: number;
  };
  startLocationName?: string;
  arrivalLocationName?: string;
}

interface NotificationReminder {
  reminderMinutes: number;
  targetTime: Timestamp;
  windowStart: Timestamp;
  windowEnd: Timestamp;
  sent: boolean;
  sentAt?: Timestamp;
  attempts: number;
  lastAttemptAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastError?: string;
}

/**
 * Check for upcoming reservations and send reminder notifications
 * This function is called from the existing calculateTripCosts cron job
 */
export async function checkUpcomingReservations(): Promise<void> {
  try {
    const now = Timestamp.now();
    const tenants = await getAllActiveTenants();

    // Process each tenant's reminders
    for (const tenantId of tenants) {
      await processDueReminders(now, tenantId);
      await ensureReminderDocumentsExist(tenantId);
    }
  } catch (error) {
    logger.error("Error checking upcoming reservations:", error);
  }
}

/**
 * Process all due reminders using collection group query
 */
async function processDueReminders(now: Timestamp, tenantId: string): Promise<void> {
  try {
    // Query reminder documents for this specific tenant that are due and not yet sent
    const tripsSnapshot = await getTenantCollection(tenantId, "trips").where("status", "==", "reserved").get();

    for (const tripDoc of tripsSnapshot.docs) {
      const remindersSnapshot = await tripDoc.ref
        .collection("notification_reminders")
        .where("sent", "==", false)
        .where("windowStart", "<=", now)
        .where("windowEnd", ">=", now)
        .where("attempts", "<", 3)
        .get();

      for (const reminderDoc of remindersSnapshot.docs) {
        const reminder = reminderDoc.data() as NotificationReminder;
        const trip = tripDoc.data() as Trip;

        // Send notifications with retry logic
        const success = await sendReservationNotificationsWithRetry(tripDoc.id, trip, reminder.reminderMinutes, tenantId);

        // Update reminder tracking
        await updateReminderTracking(reminderDoc.ref, success);
      }
    }
  } catch (error) {
    logger.error("Error processing due reminders for tenant:", { tenantId, error });
  }
}

/**
 * Ensure all reserved trips have their reminder documents created
 */
async function ensureReminderDocumentsExist(tenantId: string): Promise<void> {
  try {
    const reminderTimes = await getReservationReminderTimes(tenantId);

    // Query for reserved trips that might need reminder documents
    const reservedTripsQuery = getTenantCollection(tenantId, "trips")
      .where("status", "==", "reserved")
      .where("pickupTime", ">", Timestamp.now());

    const reservedTripsSnapshot = await reservedTripsQuery.get();

    for (const tripDoc of reservedTripsSnapshot.docs) {
      const trip = tripDoc.data() as Trip;
      const tripId = tripDoc.id;

      if (!trip.pickupTime) continue;

      await createReminderDocuments(tripId, trip.pickupTime, reminderTimes, tenantId);
    }
  } catch (error) {
    logger.error("Error ensuring reminder documents exist:", error);
  }
}

/**
 * Create reminder documents for a trip
 */
async function createReminderDocuments(
  tripId: string,
  pickupTime: Timestamp,
  reminderTimes: number[],
  tenantId: string
): Promise<void> {
  const batch = db.batch();
  const now = Timestamp.now();

  for (const reminderMinutes of reminderTimes) {
    const reminderRef = getTenantCollection(tenantId, "trips")
      .doc(tripId)
      .collection("notification_reminders")
      .doc(reminderMinutes.toString());

    // Check if reminder document already exists
    const existingReminder = await reminderRef.get();
    if (existingReminder.exists) continue;

    // Calculate time windows
    const targetTime = new Date(pickupTime.toDate().getTime() - reminderMinutes * 60 * 1000);
    const windowStart = new Date(targetTime.getTime() - 2 * 60 * 1000); // 2 minutes before
    const windowEnd = new Date(targetTime.getTime() + 1 * 60 * 1000); // 1 minute after

    const reminderData: NotificationReminder = {
      reminderMinutes,
      targetTime: Timestamp.fromDate(targetTime),
      windowStart: Timestamp.fromDate(windowStart),
      windowEnd: Timestamp.fromDate(windowEnd),
      sent: false,
      attempts: 0,
      createdAt: now,
      updatedAt: now,
    };

    batch.set(reminderRef, reminderData);
  }

  await batch.commit();
}

/**
 * Update reminder tracking with retry information
 */
async function updateReminderTracking(reminderRef: admin.firestore.DocumentReference, success: boolean): Promise<void> {
  try {
    const updateData: Partial<NotificationReminder> = {
      attempts: admin.firestore.FieldValue.increment(1) as any,
      lastAttemptAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    if (success) {
      updateData.sent = true;
      updateData.sentAt = Timestamp.now();
    }

    await reminderRef.update(updateData);
  } catch (error) {
    logger.error("Error updating reminder tracking:", { error, reminderRef: reminderRef.path });
  }
}

/**
 * Send reservation reminder notifications with retry logic
 */
async function sendReservationNotificationsWithRetry(
  tripId: string,
  trip: Trip,
  reminderMinutes: number,
  tenantId: string
): Promise<boolean> {
  try {
    await sendReservationNotifications(tripId, trip, reminderMinutes, tenantId);
    return true;
  } catch (error) {
    logger.error("Error sending reservation notifications:", {
      tripId,
      reminderMinutes,
      error,
    });
    return false;
  }
}

/**
 * Send reservation reminder notifications to passenger and admins
 */
async function sendReservationNotifications(
  tripId: string,
  trip: Trip,
  reminderMinutes: number,
  tenantId: string
): Promise<void> {
  const pickupTime = trip.pickupTime?.toDate();
  if (!pickupTime) return;

  // Time formatting is now handled by the notification system with proper locale formatting

  // Send passenger notification
  try {
    const passengerDoc = await db.collection("mobile_users").doc(trip.uidPassenger).get();
    const passengerData = passengerDoc.data();

    if (passengerData?.fcmToken) {
      const customBody = `Your trip to ${trip.arrivalLocationName || trip.arrivalLocation?.name || "destination"} is scheduled for {time}`;

      await sendPassengerNotification(
        passengerData.fcmToken,
        "reservation_reminder",
        trip.uidPassenger,
        {
          tripId,
          reminderMinutes: reminderMinutes.toString(),
          pickupTime: pickupTime.toISOString(),
        },
        customBody,
        pickupTime, // Pass the time for proper locale formatting
        tenantId
      );

      logger.info("Reservation reminder sent to passenger", {
        tripId,
        passengerId: trip.uidPassenger,
        reminderMinutes,
      });
    }
  } catch (error) {
    logger.error("Error sending passenger reservation reminder:", {
      tripId,
      error,
    });
  }

  // Send driver notification if driver is assigned
  if (trip.uidChosenDriver) {
    try {
      const driverDoc = await db.collection("mobile_users").doc(trip.uidChosenDriver).get();
      const driverData = driverDoc.data();

      if (driverData?.fcmToken) {
        // For driver reminders, we want to emphasize the pickup time
        // The notification function will handle the title and body formatting

        await sendDriverAssignmentNotification(
          driverData.fcmToken,
          tripId,
          trip.uidChosenDriver, // driverUid
          pickupTime,
          trip.arrivalLocation?.name || trip.arrivalLocationName,
          true, // isReserved
          true // isReminder
        );

        logger.info("Reservation reminder sent to driver", {
          tripId,
          driverId: trip.uidChosenDriver,
          reminderMinutes,
        });
      }
    } catch (error) {
      logger.error("Error sending driver reservation reminder:", {
        tripId,
        driverId: trip.uidChosenDriver,
        error,
      });
    }
  }

  // Send admin notification
  try {
    const locationInfo =
      trip.startLocation?.name && trip.arrivalLocation?.name
        ? `from ${trip.startLocation.name} to ${trip.arrivalLocation.name}`
        : "Trip details in app";

    await sendAdminNotification(
      "reservation_reminder",
      "Upcoming Reservation",
      `Reservation in ${reminderMinutes} minutes - ${locationInfo}`,
      {
        tripId,
        passengerId: trip.uidPassenger,
        pickupTime: pickupTime.toISOString(),
        reminderMinutes: reminderMinutes.toString(),
        type: "reservation_reminder" as const,
      },
      tenantId
    );

    logger.info("Reservation reminder sent to admins", {
      tripId,
      reminderMinutes,
    });
  } catch (error) {
    logger.error("Error sending admin reservation reminder:", {
      tripId,
      error,
    });
  }
}

/**
 * Monitor notification reliability and log metrics
 */
export async function monitorNotificationReliability(): Promise<void> {
  try {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const tenants = await getAllActiveTenants();

    const allMetrics: { [tenantId: string]: any } = {};

    for (const tenantId of tenants) {
      // Query recent completed trips for this tenant
      const recentTripsQuery = getTenantCollection(tenantId, "trips")
        .where("status", "in", ["paid", "cancelled"])
        .where("pickupTime", ">=", Timestamp.fromDate(oneDayAgo));

      const recentTripsSnapshot = await recentTripsQuery.get();

      let totalTrips = 0;
      const reminderStats: { [key: string]: { successful: number; failed: number } } = {};

      for (const tripDoc of recentTripsSnapshot.docs) {
        totalTrips++;

        // Get all reminder documents for this trip
        const remindersSnapshot = await tripDoc.ref.collection("notification_reminders").get();

        for (const reminderDoc of remindersSnapshot.docs) {
          const reminder = reminderDoc.data() as NotificationReminder;
          const reminderKey = `reminder${reminder.reminderMinutes}min`;

          if (!reminderStats[reminderKey]) {
            reminderStats[reminderKey] = { successful: 0, failed: 0 };
          }

          if (reminder.sent) {
            reminderStats[reminderKey].successful++;
          } else if (reminder.attempts > 0) {
            reminderStats[reminderKey].failed++;
          }
        }
      }

      // Calculate metrics for this tenant
      const metricsData: any = { totalTrips };

      for (const [reminderKey, stats] of Object.entries(reminderStats)) {
        const total = stats.successful + stats.failed;
        metricsData[reminderKey] = {
          successful: stats.successful,
          failed: stats.failed,
          successRate: total > 0 ? ((stats.successful / total) * 100).toFixed(2) + "%" : "N/A",
        };
      }

      allMetrics[tenantId] = metricsData;
    }

    logger.info("Notification reliability metrics by tenant (last 24h)", allMetrics);
  } catch (error) {
    logger.error("Error monitoring notification reliability:", error);
  }
}

/**
 * Create reminder documents when a trip is reserved
 * This should be called when a trip status changes to 'reserved'
 */
export async function createRemindersForReservedTrip(tripId: string, pickupTime: Timestamp, tenantId?: string): Promise<void> {
  try {
    const effectiveTenantId = tenantId || getDefaultTenantId();
    const reminderTimes = await getReservationReminderTimes(effectiveTenantId);
    await createReminderDocuments(tripId, pickupTime, reminderTimes, effectiveTenantId);

    logger.info("Created reminder documents for reserved trip", {
      tripId,
      tenantId: effectiveTenantId,
      reminderTimes,
      pickupTime: pickupTime.toDate().toISOString(),
    });
  } catch (error) {
    logger.error("Error creating reminders for reserved trip:", { tripId, tenantId, error });
  }
}
